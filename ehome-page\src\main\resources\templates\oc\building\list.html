<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('楼栋信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>楼栋名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>楼栋别称：</label>
                                <input type="text" name="alias_name"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-info" onclick="saveSort()">
                    <i class="fa fa-sort-amount-asc"></i> 保存排序
                </a>
                <a class="btn btn-warning" onclick="importBuilding()">
                    <i class="fa fa-upload"></i> 批量导入楼栋
                </a>
                <a class="btn btn-info" onclick="importBuildingUnit()">
                    <i class="fa fa-upload"></i> 导入楼栋单元
                </a>
                <a class="btn btn-primary" onclick="refreshStats()">
                    <i class="fa fa-refresh"></i> 刷新统计
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
      <script th:inline="javascript">

        var prefix = ctx + "oc/building";
        var originalOrders = {}; // 存储原始排序值

        $(function() {
            $('#formId').renderSelect({prefix:prefix});
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "楼栋信息",
                showToolbar: false,
                layer:{
                    area:['800px','500px'],
                    offset: '70px'
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'building_id',
                    title: '楼栋ID',
                    visible: false
                },
                {
                    field: 'alias_name',
                    title: '楼栋名称',
                    formatter: function(value, row, index) {
                        var _value = row.name;
                        if(value){
                            return _value + '('+ value + ')';
                        }
                        return _value || '-';
                    }
                },
                {
                    field : 'order_index',
                    title : '排序',
                    width: '10',
                    widthUnit: '%',
                    align: "center",
                    formatter: function(value, row, index) {
                        var buildingIdText = $.common.sprintf("<input type='hidden' name='buildingIds' value='%s'>", row.building_id);
                        var sortText = $.common.sprintf("<input type='text' name='orderIndexes' value='%s' class='form-control' style='display: inline-block; width:60px; text-align:center;'>", value || 0);
                        originalOrders[row.building_id] = value || 0;
                        return buildingIdText + sortText;
                    }
                },
                {
                    field: 'total_units',
                    title: '单元总数',
                    formatter: function(value, row, index) {
                        var unitLink = '';
                        if (row.total_units > 0) {
                            unitLink = ' <a href="javascript:void(0)" onclick="viewUnits(\'' + row.building_id + '\', \'' + row.name + '\')" title="查看单元" class="hover-link" style="display:none;"><i class="fa fa-external-link"></i></a>';
                        }
                        return (value || 0) + unitLink;
                    }
                },
                {
                    field: 'house_count',
                    title: '房屋数量',
                    formatter: function(value, row, index) {
                        var houseLink = '';
                        if (row.house_count > 0) {
                            houseLink = ' <a href="javascript:void(0)" onclick="viewHouses(\'' + row.building_id + '\', \'' + row.name + '\')" title="查看房屋" class="hover-link" style="display:none;"><i class="fa fa-external-link"></i></a>';
                        }
                        return (value || 0) + houseLink;
                    }
                },
                {
                    field: 'house_area',
                    title: '房屋面积',
                    formatter: function(value, row, index) {
                        if(row.house_area == '0'){
                            return "-";
                        }
                        return row.house_area + "㎡";
                    }
                },
                {
                    field: 'manager',
                    title: '楼栋管家'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a href="javascript:void(0)" onclick="$.operate.edit(\'' + row.building_id + '\')">编辑</a> ');
                        actions.push('<a href="javascript:void(0)" onclick="$.operate.remove(\'' + row.building_id + '\')">删除</a>');
                        return actions.join(' | ');
                    }
                }]
            };
            $.table.init(options);

            // 添加表格行hover效果
            $(document).on('mouseenter', '#bootstrap-table tbody tr', function() {
                $(this).find('.hover-link').show();
            }).on('mouseleave', '#bootstrap-table tbody tr', function() {
                $(this).find('.hover-link').hide();
            });
        });

        function queryCommunity(obj){
            $.table.search();
        }

        function viewHouses(buildingId, buildingName) {
            var url = ctx + 'oc/house/mgr?buildingId=' + buildingId;
            $.modal.openTab(buildingName + "房屋管理", url);
        }

        function viewUnits(buildingId, buildingName) {
            var url = ctx + 'oc/unit/list/' + buildingId;
            $.modal.popupRight(buildingName + "单元管理", url);
        }

        function refreshStats() {
            $.modal.confirm("确认要刷新所有楼栋的房屋统计信息吗？", function() {
                $.modal.loading("正在刷新统计信息，请稍候...");
                $.ajax({
                    url: prefix + "/refreshStats",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.msgError("刷新失败，请稍后重试");
                    }
                });
            });
        }

        // 批量导入楼栋
        function importBuilding() {
            var html = '<div style="padding: 10px;">';
            html += '<div class="form-group">';
            html += '<label>粘贴楼栋名称数据：</label>';
            html += '<textarea style="width:95%;" id="importData" class="form-control" rows="8" placeholder="请粘贴楼栋名称数据，每行一个楼栋名称，格式如下：&#10;1号楼&#10;2号楼&#10;3号楼&#10;A栋&#10;B栋"></textarea>';
            html += '</div>';
            html += '</div>';

            layer.open({
                type: 1,
                title: '批量导入楼栋',
                area: ['500px', '420px'],
                content: html,
                scrollbar: false,
                btn: ['解析数据', '取消'],
                yes: function(index, layero) {
                    var dataText = layero.find('#importData').val();

                    if (!dataText || dataText.trim() === '') {
                        layer.msg('请输入要导入的楼栋名称', {icon: 5});
                        return false;
                    }

                    // 解析数据
                    var importData = parseBuildingData(dataText);
                    if (importData.length === 0) {
                        layer.msg('没有解析到有效数据，请检查格式', {icon: 5});
                        return false;
                    }

                    layer.close(index);

                    // 显示确认对话框
                    showImportConfirm(importData);
                }
            });
        }

        // 解析楼栋导入数据
        function parseBuildingData(text) {
            var lines = text.split('\n');
            var data = [];

            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue;

                // 楼栋名称不能为空
                if (line) {
                    data.push({
                        buildingName: line
                    });
                }
            }

            return data;
        }

        // 显示导入确认对话框
        function showImportConfirm(importData) {
            var html = '<div style="max-height: 400px; overflow-y: auto;">';
            html += '<p>解析到 <strong>' + importData.length + '</strong> 个楼栋，将导入到当前小区：</p>';
            html += '<table class="table table-bordered table-striped" style="margin-bottom: 10px;">';
            html += '<thead><tr><th>序号</th><th>楼栋名称</th></tr></thead>';
            html += '<tbody>';

            for (var i = 0; i < importData.length; i++) {
                html += '<tr>';
                html += '<td>' + (i + 1) + '</td>';
                html += '<td>' + importData[i].buildingName + '</td>';
                html += '</tr>';
            }

            html += '</tbody></table>';
            html += '<p class="text-muted">注意：重复的楼栋名称将被跳过</p>';
            html += '</div>';

            layer.confirm(html, {
                title: '确认导入数据',
                area: ['600px', '500px'],
                btn: ['确认导入', '取消']
            }, function(index) {
                // 执行导入
                doImportBuilding(importData);
                layer.close(index);
            });
        }

        // 执行导入
        function doImportBuilding(importData) {
            var requestData = {
                importData: importData
            };

            $.ajax({
                url: prefix + "/batchImport",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(requestData),
                success: function(result) {
                    if (result.code === 0) {
                        layer.msg(result.msg || '导入成功', {icon: 1});
                        // 刷新表格
                        $.table.refresh();
                    } else {
                        layer.msg(result.msg || '导入失败', {icon: 5});
                    }
                },
                error: function() {
                    layer.msg('导入失败，请重试', {icon: 5});
                }
            });
        }

        // 导入楼栋单元
        function importBuildingUnit() {
            var html = '<div style="padding: 10px;">';
            html += '<div class="form-group">';
            html += '<label>粘贴楼栋单元数据：</label>';
            html += '<textarea style="width:95%;" id="importUnitData" class="form-control" rows="8" placeholder="请粘贴楼栋单元数据，每行格式：楼栋名称 单元名称，用空格或制表符分隔，格式如下：&#10;1号楼 1单元&#10;1号楼 2单元&#10;2号楼 1单元&#10;A栋 东单元&#10;A栋 西单元"></textarea>';
            html += '</div>';
            html += '</div>';

            layer.open({
                type: 1,
                title: '导入楼栋单元',
                area: ['500px', '420px'],
                content: html,
                scrollbar: false,
                btn: ['解析数据', '取消'],
                yes: function(index, layero) {
                    var dataText = layero.find('#importUnitData').val();

                    if (!dataText || dataText.trim() === '') {
                        layer.msg('请输入要导入的楼栋单元数据', {icon: 5});
                        return false;
                    }

                    // 解析数据
                    var importData = parseBuildingUnitData(dataText);
                    if (importData.length === 0) {
                        layer.msg('没有解析到有效数据，请检查格式', {icon: 5});
                        return false;
                    }

                    layer.close(index);

                    // 显示确认对话框
                    showImportUnitConfirm(importData);
                }
            });
        }

        // 解析楼栋单元导入数据
        function parseBuildingUnitData(text) {
            var lines = text.split('\n');
            var data = [];

            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue;

                // 用空格或制表符分割
                var parts = line.split(/\s+/);
                if (parts.length >= 2) {
                    var buildingName = parts[0].trim();
                    var unitName = parts[parts.length - 1].trim(); // 取最后一部分作为单元名称

                    // 如果有多个部分，中间的部分也加入楼栋名称
                    if (parts.length > 2) {
                        buildingName = parts.slice(0, -1).join(' ').trim();
                    }

                    if (buildingName && unitName) {
                        data.push({
                            buildingName: buildingName,
                            unitName: unitName
                        });
                    }
                }
            }

            return data;
        }

        // 显示楼栋单元导入确认对话框
        function showImportUnitConfirm(importData) {
            var html = '<div style="max-height: 400px; overflow-y: auto;">';
            html += '<p>解析到 <strong>' + importData.length + '</strong> 个楼栋单元：</p>';
            html += '<table class="table table-bordered table-striped" style="margin-bottom: 10px;">';
            html += '<thead><tr><th>序号</th><th>楼栋名称</th><th>单元名称</th></tr></thead>';
            html += '<tbody>';

            for (var i = 0; i < importData.length; i++) {
                html += '<tr>';
                html += '<td>' + (i + 1) + '</td>';
                html += '<td>' + importData[i].buildingName + '</td>';
                html += '<td>' + importData[i].unitName + '</td>';
                html += '</tr>';
            }

            html += '</tbody></table>';
            html += '<p class="text-muted">注意：楼栋必须先存在，重复的单元将被跳过</p>';
            html += '</div>';

            layer.confirm(html, {
                title: '确认导入楼栋单元数据',
                area: ['600px', '500px'],
                btn: ['确认导入', '取消']
            }, function(index) {
                // 执行导入
                doImportBuildingUnit(importData);
                layer.close(index);
            });
        }

        // 执行楼栋单元导入
        function doImportBuildingUnit(importData) {
            var requestData = {
                importData: importData
            };

            $.ajax({
                url: prefix + "/batchImportUnit",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(requestData),
                success: function(result) {
                    if (result.code === 0) {
                        layer.msg(result.msg || '导入成功', {icon: 1});
                        // 刷新表格
                        $.table.refresh();
                    } else {
                        layer.msg(result.msg || '导入失败', {icon: 5});
                    }
                },
                error: function() {
                    layer.msg('导入失败，请重试', {icon: 5});
                }
            });
        }

        /* 保存排序-楼栋 */
        function saveSort() {
            var changedBuildingIds = [];
            var changedOrderIndexes = [];
            $("input[name='buildingIds']").each(function() {
                var buildingId = $(this).val();
                var currentOrderIndex = $(this).next("input[name='orderIndexes']").val();
                if (originalOrders[buildingId] != currentOrderIndex) {
                    changedBuildingIds.push(buildingId);
                    changedOrderIndexes.push(currentOrderIndex);
                }
            });
            if (changedBuildingIds.length === 0) {
                $.modal.alertWarning("未检测到排序修改");
                return;
            }
            $.operate.post(prefix + "/updateBatchSort", { "buildingIds": changedBuildingIds.join(","), "orderIndexes": changedOrderIndexes.join(",") });
        }
    </script>
</body>
</html>